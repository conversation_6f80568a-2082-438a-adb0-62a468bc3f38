<?php
#region region DOCS
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<title>Registro Aliados Comerciales - <?php echo APP_NAME; ?></title>
	
	<?php require_once __ROOT__ . '/views/head_section.view.php'; ?>
	
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
	<link rel="stylesheet" href="<?php echo RUTA_RESOURCES ?>css/fab_formularios_web_2.css">

	<style>
		/* Service Selection Styles - Compact version for form */
		.service-selection-container {
			border: 1px solid #dee2e6;
			border-radius: 8px;
			padding: 20px;
			background-color: #f8f9fa;
		}

		/* 3. Tab Container Width Alignment - Make tabs match content width */
		.service-category-tabs {
			display: flex;
			flex-wrap: wrap;
			justify-content: center;
			gap: 10px;
			margin-bottom: 20px;
			width: 100%; /* Match the content area width */
		}

		.service-category-tabs .nav-link {
			background-color: #fff;
			border          : 1px solid #dee2e6;
			border-radius   : 8px;
			padding         : 12px 16px;
			text-align      : center;
			min-width       : 120px;
			transition      : all 0.3s ease;
			color           : #495057;
		}

		/* 1. Service Category Tab Hover Text Color - Fix hover state */
		.service-category-tabs .nav-link:hover, .service-category-tabs h6:hover {
			background-color: #007bff;
			color           : #ffffff !important;
			border-color    : #007bff;
			transform       : translateY(-2px);
			box-shadow      : 0 4px 8px rgba(0,123,255,0.2);
		}

		.service-category-tabs .nav-link.active h6 {
			color: white !important;
		}

		/* 2. Active Tab Background Color - Use same blue as hover */
		.service-category-tabs .nav-link.active {
			background-color: #007bff;                        /* Same blue as hover state */
			color           : #ffffff !important;             /* Ensure white text */
			border-color    : #007bff;
			transform       : translateY(-2px);
			box-shadow      : 0 4px 8px rgba(0,123,255,0.2);
		}

		.service-category-tabs .tab-icon {
			font-size: 18px;
			margin-bottom: 5px;
		}

		.service-category-tabs h6 {
			font-size: 12px;
			font-weight: 600;
			margin: 0;
		}

		/* 4. Remove Tab Pane Visual Contrast - Seamless integration */
		.service-category-content {
			background-color: transparent; /* Remove white background */
			border-radius: 0; /* Remove border radius */
			padding: 15px 0; /* Remove horizontal padding */
			min-height: 200px;
			border: none; /* Remove any borders */
			box-shadow: none; /* Remove any shadows */
		}

		.service-category-content .tab-pane {
			background-color: transparent; /* Remove tab pane background */
			border: none; /* Remove tab pane borders */
			box-shadow: none; /* Remove tab pane shadows */
			padding: 0; /* Remove tab pane padding */
		}

		/* 5. Service Item Checkbox and Layout Improvements */
		.service-items .form-check {
			background-color: #f8f9fa;
			border: 1px solid #e9ecef;
			border-radius: 6px;
			padding: 10px;
			margin-bottom: 8px;
			transition: all 0.2s ease;
			cursor: pointer; /* Make entire container clickable */
			position: relative; /* For proper checkbox positioning */
		}

		.service-items .form-check:hover {
			background-color: #e9ecef;
			border-color: #007bff;
		}

		/* Fix checkbox positioning within container */
		.service-items .form-check-input {
			position: relative;
			margin-top: 0;
			margin-right: 8px;
			float: none;
		}

		.service-items .form-check-label {
			cursor: pointer;
			width: 100%;
			margin-bottom: 0;
			display: flex;
			align-items: center;
		}

		.service-items .form-check-input:checked + .form-check-label {
			color: #007bff;
			font-weight: 500;
		}

		/* 5. Remove service priority number - Hide service-number */
		.service-number {
			display: none; /* Hide the priority numbers */
		}

		/* 6. "Seleccionar todos" Button Styling Enhancement */
		.select-all-btn {
			background-color: #007bff;
			color: white;
			border: 1px solid #007bff;
			border-radius: 6px;
			padding: 8px 16px;
			font-size: 14px;
			font-weight: 500;
			transition: all 0.2s ease;
			cursor: pointer;
		}

		.select-all-btn:hover {
			background-color: #0056b3;
			border-color: #0056b3;
			color: white;
			transform: translateY(-1px);
			box-shadow: 0 2px 4px rgba(0,123,255,0.2);
		}

		.select-all-btn:active {
			transform: translateY(0);
			box-shadow: 0 1px 2px rgba(0,123,255,0.2);
		}

		/* Validation styles for service selection */
		fieldset.is-invalid {
			border-color: #dc3545;
		}

		fieldset.is-invalid .invalid-feedback {
			display: block;
			color: #dc3545;
			font-size: 0.875em;
			margin-top: 0.25rem;
		}

		/* Mobile responsive */
		@media (max-width: 768px) {
			.service-category-tabs .nav-link {
				min-width: 100px;
				padding: 10px 12px;
			}

			.service-category-tabs h6 {
				font-size: 11px;
			}

			.service-category-tabs .tab-icon {
				font-size: 16px;
			}

			.select-all-btn {
				font-size: 12px;
				padding: 6px 12px;
			}
		}
	</style>
</head>
<body class="body-wrapper">
<!-- header__area start -->
<?php require_once __ROOT__ . '/views/header_menu_section.view.php'; ?>
<!-- header__area end -->

<main>
	<?php #region region form ?>
		<div class="container mt-5" id="contenido_formulario">
			<h1 class="mb-4">Aliado comercial</h1>
			<p class="text-muted mb-4">Completa el formulario para registrar tu alianza o referir una oportunidad de negocio.</p>
			
			<?php // Removed PHP Session Message Display Block ?>
			
			<ul class="nav nav-tabs nav-fill" id="aliadoTab" role="tablist">
				<li class="nav-item" role="presentation">
					<button class="nav-link active" id="aliado-info-tab" data-bs-toggle="tab" data-bs-target="#aliado-info" type="button" role="tab" aria-controls="aliado-info" aria-selected="true">
						1. Info Aliado
					</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="cliente-info-tab" data-bs-toggle="tab" data-bs-target="#cliente-info" type="button" role="tab" aria-controls="cliente-info" aria-selected="false">
						2. Cliente Potencial
					</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="oportunidad-info-tab" data-bs-toggle="tab" data-bs-target="#oportunidad-info" type="button" role="tab" aria-controls="oportunidad-info" aria-selected="false">
						3. Oportunidad
					</button>
				</li>
				<li class="nav-item" role="presentation">
					<button class="nav-link" id="declaracion-tab" data-bs-toggle="tab" data-bs-target="#declaracion" type="button" role="tab" aria-controls="declaracion" aria-selected="false">
						4. Declaración
					</button>
				</li>
			</ul>
			
			<form id="registro-aliado-form" class="needs-validation" novalidate method="POST" action="src/form_aliados_comerciales.php">
				<div class="tab-content" id="aliadoTabContent">
					
					<?php #region region TAB informacion aliado ?>
					<div class="tab-pane fade show active" id="aliado-info" role="tabpanel" aria-labelledby="aliado-info-tab" tabindex="0">
						<h5 class="mt-3 mb-3">Información del Aliado Comercial</h5>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="nombre_razon_social" class="form-label">Nombre completo / Razón social:</label>
								<input type="text" class="form-control" id="nombre_razon_social" name="nombre_razon_social" required>
								<div class="invalid-feedback">Ingresa el nombre o razón social.</div>
							</div>
							<div class="col-md-6 mb-3">
								<label for="cedula_nit" class="form-label">Cédula / NIT:</label>
								<input type="text" class="form-control" id="cedula_nit" name="cedula_nit" placeholder="Ingrese el número sin puntos ni comas" required>
								<div class="invalid-feedback">Ingresa la cédula o NIT.</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="telefono_contacto" class="form-label">Teléfono de contacto:</label>
								<input type="tel" class="form-control" id="telefono_contacto" name="telefono_contacto" placeholder="Ej: +57 ************" required pattern="^\+?[0-9\s\-()]{7,}$">
								<div class="invalid-feedback">Ingresa un teléfono válido.</div>
							</div>
							<div class="col-md-6 mb-3">
								<label for="correo_electronico" class="form-label">Correo electrónico:</label>
								<input type="email" class="form-control" id="correo_electronico" name="correo_electronico" placeholder="<EMAIL>" required>
								<div class="invalid-feedback">Ingresa un correo válido.</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="ciudad_operacion" class="form-label">Ciudad de operación:</label>
								<input type="text" class="form-control" id="ciudad_operacion" name="ciudad_operacion" placeholder="Ej: Barranquilla" required>
								<div class="invalid-feedback">Ingresa la ciudad de operación.</div>
							</div>
							<div class="col-md-6 mb-3">
								<label for="pais_operacion" class="form-label">País de operación:</label>
								<input type="text" class="form-control" id="pais_operacion" name="pais_operacion" placeholder="Ej: Colombia" required>
								<div class="invalid-feedback">Ingresa el país de operación.</div>
							</div>
						</div>

						<fieldset class="mb-3">
							<legend class="form-label fs-6">Tipo de Alianza Comercial (Selecciona una)</legend>
							<div class="form-check">
								<input class="form-check-input" type="radio" name="tipo_alianza" id="tipo_socio" value="Socio estrategico" required>
								<label class="form-check-label" for="tipo_socio">
									Socio Estratégico (Participación activa en proyectos con porcentaje de ganancia)
								</label>
							</div>
							<div class="form-check">
								<input class="form-check-input" type="radio" name="tipo_alianza" id="tipo_embajador" value="Embajador comercial" required>
								<label class="form-check-label" for="tipo_embajador">
									Embajador Comercial (Recomienda y recibe comisión por negocio cerrado)
								</label>
								<div class="invalid-feedback">Selecciona un tipo de alianza.</div>
							</div>
						</fieldset>
						<div class="d-flex justify-content-end mt-4">
							<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
						</div>
					</div>
					<?php #endregion TAB informacion aliado ?>
					<?php #region region TAB cliente potencial ?>
					<div class="tab-pane fade" id="cliente-info" role="tabpanel" aria-labelledby="cliente-info-tab" tabindex="0">
						<h5 class="mt-3 mb-3">Información del Cliente Potencial</h5>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="nombre_empresa_cliente" class="form-label">Nombre de la empresa/cliente:</label>
								<input type="text" class="form-control" id="nombre_empresa_cliente" name="nombre_empresa_cliente" required>
								<div class="invalid-feedback">Ingresa el nombre de la empresa o cliente.</div>
							</div>
							<div class="col-md-6 mb-3">
								<label for="nombre_contacto_cliente" class="form-label">Nombre del contacto en la empresa:</label>
								<input type="text" class="form-control" id="nombre_contacto_cliente" name="nombre_contacto_cliente" required>
								<div class="invalid-feedback">Ingresa el nombre del contacto.</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="cargo_contacto_cliente" class="form-label">Cargo del contacto:</label>
								<input type="text" class="form-control" id="cargo_contacto_cliente" name="cargo_contacto_cliente" placeholder="Ej: CEO, gerente de ventas, etc.">
							</div>
							<div class="col-md-6 mb-3">
								<label for="telefono_contacto_cliente" class="form-label">Teléfono del contacto:</label>
								<input type="tel" class="form-control" id="telefono_contacto_cliente" name="telefono_contacto_cliente" placeholder="Ej: +57 ************" required pattern="^\+?[0-9\s\-()]{7,}$">
								<div class="invalid-feedback">Ingresa un teléfono válido para el contacto.</div>
							</div>
						</div>
						<div class="row">
							<div class="col-md-6 mb-3">
								<label for="correo_contacto_cliente" class="form-label">Correo electrónico del contacto:</label>
								<input type="email" class="form-control" id="correo_contacto_cliente" name="correo_contacto_cliente" placeholder="<EMAIL>" required>
								<div class="invalid-feedback">Ingresa un correo válido para el contacto.</div>
							</div>
							<div class="col-md-3 mb-3">
								<label for="ciudad_cliente" class="form-label">Ciudad del cliente:</label>
								<input type="text" class="form-control" id="ciudad_cliente" name="ciudad_cliente" placeholder="Ej: Barranquilla" required>
								<div class="invalid-feedback">Ingresa la ciudad del cliente.</div>
							</div>
							<div class="col-md-3 mb-3">
								<label for="pais_cliente" class="form-label">País del cliente:</label>
								<input type="text" class="form-control" id="pais_cliente" name="pais_cliente" placeholder="Ej: Colombia" required>
								<div class="invalid-feedback">Ingresa el país del cliente.</div>
							</div>
						</div>
						<div class="d-flex justify-content-between mt-4">
							<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
							<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
						</div>
					</div>
					<?php #endregion TAB cliente potencial ?>
					<?php #region region TAB oportunidad ?>
					<div class="tab-pane fade" id="oportunidad-info" role="tabpanel" aria-labelledby="oportunidad-info-tab" tabindex="0">

						<?php #region region Servicios a solicitar ?>
						<div class="mb-4">
							<h5 class="mt-3 mb-3">Servicios a solicitar</h5>
							<p class="text-muted mb-3">Seleccione los servicios que el cliente potencial necesita. Debe seleccionar al menos un servicio.</p>

							<fieldset data-group-required="true" class="service-selection-container">
								<?php if (!empty($categorias_servicios)): ?>
									<!-- Service Category Tabs -->
									<ul class="service-category-tabs nav nav-pills mb-3" id="serviceTabs" role="tablist">
										<?php $n_categoria = 0; ?>
										<?php foreach ($categorias_servicios as $categoria_servicio): ?>
											<li class="nav-item category-tab" role="presentation">
												<button class="nav-link <?php echo ($n_categoria == 0) ? 'active' : ''; ?>"
														id="categoria-<?php echo $categoria_servicio->getId(); ?>-tab"
														data-bs-toggle="pill"
														data-bs-target="#categoria-<?php echo $categoria_servicio->getId(); ?>"
														type="button"
														role="tab"
														aria-controls="categoria-<?php echo $categoria_servicio->getId(); ?>"
														aria-selected="<?php echo ($n_categoria == 0) ? 'true' : 'false'; ?>">
													<div class="tab-icon"><i class="<?php echo $categoria_servicio->getIcono(); ?>"></i></div>
													<h6 class="mb-0"><?php echo $categoria_servicio->getDescripcion(); ?></h6>
												</button>
											</li>
											<?php $n_categoria++; ?>
										<?php endforeach; ?>
									</ul>

									<!-- Service Category Content -->
									<div class="tab-content service-category-content" id="serviceTabContent">
										<?php $n_categoria = 0; ?>
										<?php foreach ($categorias_servicios as $categoria_servicio): ?>
											<div class="tab-pane fade <?php echo ($n_categoria == 0) ? 'show active' : ''; ?>"
												 id="categoria-<?php echo $categoria_servicio->getId(); ?>"
												 role="tabpanel"
												 aria-labelledby="categoria-<?php echo $categoria_servicio->getId(); ?>-tab">

												<div class="d-flex justify-content-between align-items-center mb-3">
													<h6 class="mb-0"><?php echo $categoria_servicio->getDescripcion(); ?></h6>
													<button type="button" class="btn btn-sm btn-primary select-all-btn"
															data-category="<?php echo $categoria_servicio->getId(); ?>">
														Seleccionar todos
													</button>
												</div>

												<div class="service-items row">
													<?php foreach ($categoria_servicio->getServicios() as $servicio): ?>
														<div class="col-md-6 mb-2">
															<div class="form-check service-item" onclick="toggleServiceCheckbox('servicio_<?php echo $servicio->getId(); ?>')">
																<input class="form-check-input"
																	   type="checkbox"
																	   value="<?php echo $servicio->getId(); ?>"
																	   id="servicio_<?php echo $servicio->getId(); ?>"
																	   name="servicios[]"
																	   data-category="<?php echo $categoria_servicio->getId(); ?>">
																<label class="form-check-label" for="servicio_<?php echo $servicio->getId(); ?>">
																	<span class="service-number"><?php echo $servicio->getPrioridad(); ?></span>
																	<?php echo $servicio->getDescripcion(); ?>
																</label>
															</div>
														</div>
													<?php endforeach; ?>
												</div>
											</div>
											<?php $n_categoria++; ?>
										<?php endforeach; ?>
									</div>
								<?php else: ?>
									<div class="alert alert-warning">
										<i class="bi bi-exclamation-triangle"></i>
										No hay servicios disponibles en este momento.
									</div>
								<?php endif; ?>

								<div class="invalid-feedback">Debe seleccionar al menos un servicio.</div>
							</fieldset>
						</div>
						<?php #endregion Servicios a solicitar ?>

						<h5 class="mt-4 mb-3">Descripción de la Oportunidad</h5>

						<div class="mb-3">
							<label for="descripcion_negocio" class="form-label">Descripción del Negocio o Proyecto Referenciado:</label>
							<textarea class="form-control" id="descripcion_negocio" name="descripcion_negocio" rows="4" placeholder="Brinde un resumen del negocio potencial, necesidades del cliente y cómo <?php echo NOMBRE_EMPRESA_ABREV; ?> puede aportar valor." required></textarea>
							<div class="invalid-feedback">Describe la oportunidad de negocio.</div>
						</div>

						<div class="d-flex justify-content-between mt-4">
							<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
							<button type="button" class="btn btn-primary btn-lg siguiente-tab">Siguiente <i class="bi bi-arrow-right"></i></button>
						</div>
					</div>
					<?php #endregion TAB oportunidad ?>
					
					<?php #region region TAB declaracion ?>
					<div class="tab-pane fade" id="declaracion" role="tabpanel" aria-labelledby="declaracion-tab" tabindex="0">
						<h5 class="mt-3 mb-3">Declaración y Aceptación</h5>
						<div class="form-check mb-4">
							<input class="form-check-input" type="checkbox" value="1" id="declaracion_veracidad" name="declaracion_veracidad" required>
							<label class="form-check-label" for="declaracion_veracidad">
								Declaro que la información suministrada es verídica y autorizo a <strong><?php echo NOMBRE_EMPRESA_ABREV; ?></strong> a contactarme para formalizar la relación comercial y/o contactar al cliente potencial referido.
							</label>
							<div class="invalid-feedback">Debes aceptar la declaración para continuar.</div>
						</div>
						<div class="form-check mb-4">
							<input class="form-check-input" type="checkbox" value="1" id="aceptacion_terminos" name="aceptacion_terminos" required>
							<label class="form-check-label" for="aceptacion_terminos">
								He leído y acepto los <a href="politica_aceptacion_aliado_comercial" target="_blank" rel="noopener noreferrer">Términos y Condiciones de la política de declaración y aceptación de aliados comerciales</a>.
							</label>
							<div class="invalid-feedback">Debes aceptar la declaración para continuar.</div>
						</div>
						
						<div class="alert alert-success small d-flex align-items-center" role="alert">
							<i class="bi bi-envelope-check-fill me-2"></i>
							<div><strong><?php echo NOMBRE_EMPRESA_ABREV; ?></strong> se pondrá en contacto para analizar la oportunidad y coordinar los siguientes pasos. ¡Gracias por formar parte de nuestra red de aliados comerciales!</div>
						</div>
						
						<div class="d-flex justify-content-between mt-4">
							<button type="button" class="btn btn-secondary btn-lg anterior-tab"><i class="bi bi-arrow-left"></i> Anterior</button>
							<button type="submit" class="btn btn-success btn-lg" id="submit-aliado-button"><i class="bi bi-check-circle-fill"></i> Enviar Registro</button>
						</div>
					</div>
					<?php #endregion TAB declaracion ?>
				</div>
			</form>
		</div>
</main>

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/js_section.view.php'; ?>

<script>
    // Function to toggle service checkbox when clicking on the container
    function toggleServiceCheckbox(checkboxId) {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            // Trigger change event to update "Select All" button text
            checkbox.dispatchEvent(new Event('change'));
        }
    }

    document.addEventListener('DOMContentLoaded', () => {

        const mainForm = document.getElementById('registro-aliado-form');
        const tabTriggers = Array.from(document.querySelectorAll('#aliadoTab button[data-bs-toggle="tab"]'));
        const submitButton = document.getElementById('submit-aliado-button');
        
        // Initialize Bootstrap Tabs if Bootstrap JS is loaded
        if (typeof bootstrap !== 'undefined') {
            tabTriggers.forEach(triggerEl => {
                new bootstrap.Tab(triggerEl);
            });
        } else {
            console.error('Bootstrap JS not loaded, tabs might not function correctly.');
        }

        // --- Service Selection Functionality ---
        // Handle "Select All" buttons
        document.querySelectorAll('.select-all-btn').forEach(button => {
            button.addEventListener('click', () => {
                const categoryId = button.getAttribute('data-category');
                const checkboxes = document.querySelectorAll(`input[type="checkbox"][data-category="${categoryId}"]`);
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);

                // Toggle all checkboxes in this category
                checkboxes.forEach(checkbox => {
                    checkbox.checked = !allChecked;
                });

                // Update button text
                button.textContent = allChecked ? 'Seleccionar todos' : 'Deseleccionar todos';
            });
        });

        // Update "Select All" button text when individual checkboxes change
        document.querySelectorAll('input[type="checkbox"][name="servicios[]"]').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                const categoryId = checkbox.getAttribute('data-category');
                const categoryCheckboxes = document.querySelectorAll(`input[type="checkbox"][data-category="${categoryId}"]`);
                const selectAllBtn = document.querySelector(`.select-all-btn[data-category="${categoryId}"]`);

                if (selectAllBtn) {
                    const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);
                    selectAllBtn.textContent = allChecked ? 'Deseleccionar todos' : 'Seleccionar todos';
                }
            });
        });
        
        
        // Function to switch tabs programmatically using Bootstrap API
        const switchTab = (targetTabButtonId) => { // Expects button ID like 'aliado-info-tab'
            const targetTabButton = document.getElementById(targetTabButtonId);
            // Check if Bootstrap and the button exist
            if (targetTabButton && typeof bootstrap !== 'undefined') {
                const tabInstance = bootstrap.Tab.getInstance(targetTabButton) || new bootstrap.Tab(targetTabButton);
                tabInstance.show();
                // Scroll to top of form container after tab switch
                document.getElementById('contenido_formulario_aliados')?.scrollIntoView({ behavior: 'smooth', block: 'start' });
            } else {
                if (targetTabButtonId) console.error(`Tab button with ID ${targetTabButtonId} not found or Bootstrap JS missing.`);
            }
        };
        
        // Function to validate the form section within a given pane element
        const validatePane = (paneElement) => {
            if (!paneElement) return false;
            
            paneElement.classList.remove('was-validated'); // Reset validation state visually first
            let isPaneValid = true;
            
            // Standard required fields
            const elementsToValidate = paneElement.querySelectorAll('input[required], select[required], textarea[required]');
            elementsToValidate.forEach(el => {
                el.classList.remove('is-invalid'); // Clear previous invalid state
                if (!el.checkValidity()) {
                    isPaneValid = false;
                    el.classList.add('is-invalid'); // Mark as invalid if check fails
                }
            });
            
            // Radio groups
            const radioGroups = {};
            paneElement.querySelectorAll('input[type="radio"][required]').forEach(radio => {
                const name = radio.name;
                const groupContainer = radio.closest('.form-check'); // Check parent
                groupContainer?.classList.remove('is-invalid'); // Clear group invalid state
                
                if (radioGroups[name] === undefined) radioGroups[name] = false;
                if (radio.checked) radioGroups[name] = true;
            });
            for (const name in radioGroups) {
                if (!radioGroups[name]) {
                    isPaneValid = false;
                    // Mark first radio as invalid to trigger feedback
                    const firstRadioInGroup = paneElement.querySelector(`input[type="radio"][name="${name}"][required]`);
                    firstRadioInGroup?.classList.add('is-invalid');
                    // Add invalid class to parent for potential styling
                    firstRadioInGroup?.closest('.form-check')?.classList.add('is-invalid');
                }
            }
            
            
            // Checkbox groups marked with data-group-required="true"
            const checkboxGroups = paneElement.querySelectorAll('fieldset[data-group-required="true"]');
            checkboxGroups.forEach(group => {
                group.classList.remove('is-invalid'); // Reset group style
                group.querySelector('.invalid-feedback')?.classList.remove('d-block'); // Hide message
                const checkboxes = group.querySelectorAll('input[type="checkbox"]');
                const isGroupValid = Array.from(checkboxes).some(cb => cb.checked);
                if (!isGroupValid) {
                    isPaneValid = false;
                    group.classList.add('is-invalid'); // Style fieldset
                    group.querySelector('.invalid-feedback')?.classList.add('d-block'); // Show message
                }
            });
            
            // Apply 'was-validated' to the pane to show feedback messages
            paneElement.classList.add('was-validated');
            
            if (!isPaneValid) {
                console.warn(`Validation failed in tab: ${paneElement.id}`);
                const firstInvalid = paneElement.querySelector('.is-invalid, :invalid, fieldset.is-invalid'); // Broader selector
                firstInvalid?.focus(); // Focus first invalid element
            }
            
            return isPaneValid;
        };
        
        // --- Navigation Button Listeners (Validation ALWAYS runs now) ---
        document.querySelectorAll('.siguiente-tab').forEach(button => {
            button.addEventListener('click', () => {
                const currentPane = button.closest('.tab-pane');
                // Always validate before proceeding
                if (validatePane(currentPane)) {
                    const nextPane = currentPane?.nextElementSibling;
                    if (nextPane && nextPane.classList.contains('tab-pane')) {
                        switchTab(nextPane.id + '-tab'); // Switch using the button ID convention
                    }
                }
            });
        });
        
        // "Anterior" button logic remains unchanged (no validation)
        document.querySelectorAll('.anterior-tab').forEach(button => {
            button.addEventListener('click', () => {
                const currentPane = button.closest('.tab-pane');
                const prevPane = currentPane?.previousElementSibling;
                if (prevPane && prevPane.classList.contains('tab-pane')) {
                    switchTab(prevPane.id + '-tab'); // Switch using the button ID convention
                }
            });
        });
        // --- End Navigation Button Listeners ---
        
        
        // --- Final Form Submission Logic ---
        if (mainForm && submitButton) {
            mainForm.addEventListener('submit', async (event) => {
                event.preventDefault(); // Prevent default browser submission
                
                let allTabsValid = true;
                const allPanes = mainForm.querySelectorAll('.tab-pane');
                
                // Validate all panes sequentially before submitting
                for (const pane of allPanes) {
                    if (!validatePane(pane)) { // Validate each pane
                        allTabsValid = false;
                        const errorTabButtonId = pane.id + '-tab';
                        switchTab(errorTabButtonId); // Switch to the tab with the error
						showSweetAlertError(`Por favor, corrige los errores en la pestaña "${document.getElementById(errorTabButtonId)?.textContent || pane.id}"`);
                        break; // Stop checking
                    }
                }
                
                // If any tab failed validation, stop submission
                if (!allTabsValid) {
                    // Show error using SweetAlert instead of standard alert
                    const firstErrorPane = mainForm.querySelector('.tab-pane .is-invalid, .tab-pane :invalid, .tab-pane fieldset.is-invalid')?.closest('.tab-pane');
                    const errorTabButtonId = firstErrorPane?.id + '-tab';
                    const errorTabButtonText = document.getElementById(errorTabButtonId)?.textContent || firstErrorPane?.id || 'la pestaña con errores';
                    showSweetAlertError('Error de validación', `Por favor, corrige los errores indicados en ${errorTabButtonText}`);
                    return;
                }
                
                // --- If all tabs valid, collect data ---
                const formData = new FormData(mainForm);
                formData.append('is_ajax', '1'); // Mark as AJAX request
                
                // --- Optional: Display collected data for debugging ---
                // console.log("--- FormData to be sent ---");
                // for (let [key, value] of formData.entries()) {
                //     console.log(`${key}:`, value instanceof File ? value.name : value);
                // }
                // return; // Stop before sending for debugging
                
                // --- Actual Submission ---
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Enviando...';
                
                try {
                    // Use the form's action attribute for the endpoint
                    const response = await fetch(mainForm.action, {
                        method: 'POST',
                        body: formData
                        // No 'Content-Type' header needed for FormData
                    });

                    // First, get the raw response text
                    const responseText = await response.text();
                    console.log("Raw server response:", responseText); // Log the raw text

                    // Now, try to parse the text as JSON
                    try {
                        const result = JSON.parse(responseText);

                        if (response.ok && result.success) {
                            // Show success message and redirect to index
                            swal({
                                title: '¡Éxito!',
                                text: result.message || '¡Registro enviado con éxito! Gracias por unirte a nuestra red de aliados.',
                                icon: 'success',
                                button: {
                                    text: "Continuar",
                                    value: true,
                                    visible: true,
                                    className: "btn-success",
                                    closeModal: true
                                }
                            }).then(() => {
                                // Redirect to index page after user closes the success alert
                                window.location.href = 'index.php';
                            });
                    } else {
                        // Use SweetAlert for error (HTTP error or success: false)
                        console.error('Submission failed:', response.status, result);
                        let errorMessage = `Error: ${result.message || response.statusText}`;
                        if (result.errors) { // Optional: Display specific field errors if provided
                            errorMessage += "\nDetalles: " + JSON.stringify(result.errors);
                        }
                            showSweetAlertError('Error al enviar', `No se pudo guardar el registro.${errorMessage.replace(/\n/g, '<br>')}`);
                        }
                    } catch (parseError) {
                        // Handle JSON parsing error
                        console.error('Error parsing server response as JSON:', parseError);
                        console.error('Raw response that failed parsing:', responseText); // Log again for context
                        showSweetAlertError('Error de Respuesta', `El servidor envió una respuesta inesperada.`);
                    }
                } catch (error) {
                    // Use SweetAlert for network errors (fetch failed)
                    console.error('Network error during submission:', error);
                    showSweetAlertError('Error de comunicación', 'Error de red o respuesta inválida del servidor. Por favor, verifica tu conexión e inténtalo de nuevo.');
                } finally {
                    // Re-enable button
                    submitButton.disabled = false;
                    submitButton.innerHTML = '<i class="bi bi-check-circle-fill"></i> Enviar Registro';
                }
            });
        } else {
            console.error("Could not find form '#registro-aliado-form' or button '#submit-aliado-button'");
        }
    });
</script>
<?php #endregion JS ?>
</body>
</html>
