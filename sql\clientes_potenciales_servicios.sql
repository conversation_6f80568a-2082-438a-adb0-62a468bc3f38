-- Create clientes_potenciales_servicios table
-- This table stores the many-to-many relationship between potential clients and services
CREATE TABLE IF NOT EXISTS clientes_potenciales_servicios (
    id INT AUTO_INCREMENT PRIMARY KEY,
    id_cliente_potencial INT NOT NULL,
    id_servicio INT NOT NULL,
    fecha_registro DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (id_cliente_potencial) REFERENCES clientes_potenciales(id) ON UPDATE CASCADE ON DELETE CASCADE,
    FOREIGN KEY (id_servicio) REFERENCES servicios(id) ON UPDATE CASCADE ON DELETE CASCADE,
    UNIQUE KEY unique_cliente_servicio (id_cliente_potencial, id_servicio)
) COLLATE=utf8mb4_unicode_ci;

-- Create indexes for better performance
CREATE INDEX idx_id_cliente_potencial ON clientes_potenciales_servicios(id_cliente_potencial);
CREATE INDEX idx_id_servicio ON clientes_potenciales_servicios(id_servicio);
CREATE INDEX idx_fecha_registro ON clientes_potenciales_servicios(fecha_registro);
